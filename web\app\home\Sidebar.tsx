import React from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { fetchConversations } from '@/service/share'
import type { ConversationItem } from '@/models/share'
import { Divider } from 'antd'
import { HistoryOutlined } from '@ant-design/icons'
import { COMMON } from './common'
import home from '@/assets/images/sidebar/home.png'
import homeActive from '@/assets/images/sidebar/homeActive.png'
import appCenter from '@/assets/images/sidebar/appCenter.png'
import appCenterActive from '@/assets/images/sidebar/appCenterActive.png'
import knowledge from '@/assets/images/sidebar/knowledge.png'
import knowledgeActive from '@/assets/images/sidebar/knowledgeActive.png'
import material from '@/assets/images/sidebar/material.png'
import materialActive from '@/assets/images/sidebar/materialActive.png'
import search from '@/assets/images/sidebar/search.png'
import searchActive from '@/assets/images/sidebar/searchActive.png'

const menus = [
  { key: 'HomePage', label: '首页', icon: home, activeIcon: homeActive },
  { key: 'AppCenterPage', label: '应用中心', icon: appCenter, activeIcon: appCenterActive },
  { key: 'SearchPage', label: '智能学习搜索', icon: search, activeIcon: searchActive },
  { key: 'KnowledgePage', label: '知识库', icon: knowledge, activeIcon: knowledgeActive },
  { key: 'MaterialPage', label: '素材库', icon: material, activeIcon: materialActive },
]

// 历史记录
function SimpleChatHistory({ selectedKey }: { selectedKey?: string }) {
  const [history, setHistory] = useState<ConversationItem[]>([])
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const selectedConversationId = searchParams.get('conversationId')
  useEffect(() => {
    fetchConversations(true, COMMON.appId, undefined, false, 10).then((res) => {
      setHistory(res.data || [])
    }).catch((err) => {
  if (err.isHomePageUnauth && window.showHomeLoginDialog)
    window.showHomeLoginDialog()
})
  }, [selectedKey]) // 依赖selectedKey

  // 始终在顶部插入新建项
  const newChatItem: ConversationItem = { id: '', name: '最新对话', inputs: {}, introduction: '' }
  const displayHistory = [newChatItem, ...history]

  const isChatPage = pathname.startsWith(`/home/<USER>/${COMMON.appId}`)

  return (
    <ul className="mb-2 truncate">
      {displayHistory.map((item, index) => {
        const isSelected = selectedConversationId ? selectedConversationId === item.id : (isChatPage && item.id === '')
        return (
          <li
            key={index}
            className={
              `font-[PingFang SC] cursor-pointer truncate rounded px-2 text-[12px] leading-[34px] ${isSelected ? 'rounded-[11px] bg-[#FFFFFF] font-[500] text-[#3C3C3C]' : 'text-[#7C7D7E]'} `
            }
            onClick={() => {
              if (item.id === '') {
                // 跳转到新建对话页（不带 conversationId 参数）
                router.push(`/home/<USER>/${COMMON.appId}`)
              }
              else {
                router.push(`/home/<USER>/${COMMON.appId}?conversationId=${item.id}`)
              }
            }}
          >
            {item.name || '未命名会话'}
          </li>
        )
      })}
    </ul>
  )
}

export default function Sidebar({
  onSelect,
  selectedKey,
}: {
  onSelect?: (key: string) => void;
  selectedKey?: string;
}) {
  const router = useRouter()
  return (
    <>
      <aside className="flex w-56 flex-col  px-[12px] py-6 text-left font-[PingFangSC] text-[14px] font-[500] leading-[40px] text-[#181818]">
        <div className="mb-6">
          <ul className="space-y-2">
            <li className='pl-[11px] text-[18px] font-[400] leading-[40px] text-[#000000]'>{COMMON.sidebarTitle}</li>
            {menus.map((menu, index) => (
              <div key={index} className={`${selectedKey === menu.key ? 'rounded-[11px] border-[#C7D7F9] bg-[#E6EBF7] text-[#0266FF]' : ''} flex items-center gap-2 pl-[11px]`}>
                {
                  selectedKey === menu.key ? <img src={menu.activeIcon.src} alt={menu.label} className='h-[11px] w-[11px]' /> : <img src={menu.icon.src} alt={menu.label} className='h-[11px] w-[11px]' />
                }
                <li
                key={index}
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  onSelect && onSelect(menu.key)
                  localStorage.setItem('selectedMenu', menu.key)
                  if (menu.key === 'HomePage')
                    router.push('/home')
                  else
                    router.push(`/home/<USER>
                }}
                >

                {menu.label}
              </li>
            </div>
            ))}
          </ul>
        </div>
        <Divider />
       <div className='flex flex-col justify-center px-5'>
         <div className="flex justify-between text-[14px] font-[400] leading-[40px] text-[#181818]">
            <span className='flex gap-1'><HistoryOutlined />历史记录</span>
            <span className='cursor-pointer text-[12px] text-[#7C7D7E]' onClick={() => router.push('/home/<USER>')}>更多</span>
         </div>
         <div className="flex flex-col gap-2">
          <SimpleChatHistory selectedKey={selectedKey} />
         </div>
       </div>
      </aside>
    </>
  )
}

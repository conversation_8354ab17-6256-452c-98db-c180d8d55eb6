import { Button } from 'antd'
import { PlusSquareOutlined } from '@ant-design/icons'
import { COMMON } from '@/app/home/<USER>'
import { useRouter } from 'next/navigation'
export default function AppCenterItem({ app }: { app: any }) {
const router = useRouter()
  const toHelper = () => {
    const searchParams = new URLSearchParams({
      appId: app.installed_app_id || '',
      appName: app.name || '',
      appDescription: app.description || '',
    })
    router.push(`/home/<USER>/AppHelper?${searchParams.toString()}`)
  }
  return (
    <>
      <div className="group flex h-[160px] w-full cursor-pointer flex-col gap-[21px] rounded-[12px] border-[1px] border-[E9EAF2] bg-[#FFFFFF] p-[21px] font-[PingFangSC] transition-shadow duration-200 hover:shadow-lg"
        onClick={toHelper}
      >
        <div className="flex h-[70px] gap-[16px]">
          <div className="flex size-[60px] items-center justify-center rounded-[50%] bg-[#E9EAF2] text-[40px]">
            {app.icon}
          </div>
          <div className="flex flex-col gap-[8px]">
            <div className="truncate text-[14px] font-[500] leading-[20px] text-[#191919]">
              {app.name}
            </div>
            <div className="line-clamp-2 text-[12px] font-[400] leading-[20px] text-[#9192A9]">
              {app.description}
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center">
          <Button
            color="default"
            variant="outlined"
            icon={<PlusSquareOutlined />}
            iconPosition="start"
            className="border-[#FFFFFF] px-[100px] py-[7px] text-[12px] leading-[18px] opacity-0 transition-opacity duration-200 group-hover:opacity-100"
            style={{ boxShadow: '0px 0px 14px 0px rgba(176,190,224,0.4)' }}
            onClick={(e) => {
              e.stopPropagation()
              console.log('222')
             } }
          >
            {COMMON.appCenterButtonSelectText}
          </Button>
        </div>
      </div>
    </>
  )
}
